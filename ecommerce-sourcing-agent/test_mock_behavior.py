#!/usr/bin/env python3
"""
Test script to verify the mock data behavior changes.
This script tests both USE_MOCK_DATA=true and USE_MOCK_DATA=false modes.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the functions we want to test
from ecommerce_sourcing_agent import search_amazon_hot_products, find_1688_suppliers

def test_mock_mode():
    """Test with USE_MOCK_DATA=true"""
    print("=" * 60)
    print("🎭 Testing MOCK MODE (USE_MOCK_DATA=true)")
    print("=" * 60)
    
    # Set mock mode
    os.environ["USE_MOCK_DATA"] = "true"
    
    # Test Amazon search
    print("\n📦 Testing Amazon search in mock mode...")
    amazon_results = search_amazon_hot_products(
        keywords="wireless headphones",
        product_category="Electronics",
        price_range_min=20.0,
        price_range_max=100.0,
        min_rating=4.0,
        max_results=3
    )
    print(f"   Results count: {len(amazon_results)}")
    if amazon_results:
        print(f"   First product: {amazon_results[0]['title']}")
    
    # Test 1688 search
    print("\n🏭 Testing 1688 search in mock mode...")
    supplier_results = find_1688_suppliers(
        product_title="wireless headphones",
        target_price=15.0,
        minimum_order_qty=100,
        supplier_location="广东",
        max_suppliers=3
    )
    print(f"   Results count: {len(supplier_results)}")
    if supplier_results:
        print(f"   First supplier: {supplier_results[0]['supplier_name']}")

def test_real_api_mode():
    """Test with USE_MOCK_DATA=false"""
    print("\n" + "=" * 60)
    print("🌐 Testing REAL API MODE (USE_MOCK_DATA=false)")
    print("=" * 60)
    
    # Set real API mode
    os.environ["USE_MOCK_DATA"] = "false"
    
    # Test Amazon search
    print("\n📦 Testing Amazon search in real API mode...")
    amazon_results = search_amazon_hot_products(
        keywords="wireless headphones",
        product_category="Electronics",
        price_range_min=20.0,
        price_range_max=100.0,
        min_rating=4.0,
        max_results=3
    )
    print(f"   Results count: {len(amazon_results)}")
    if amazon_results:
        print(f"   First product: {amazon_results[0]['title']}")
    else:
        print("   No results returned (expected if API fails or returns empty)")
    
    # Test 1688 search with image URL
    print("\n🏭 Testing 1688 search in real API mode...")
    supplier_results = find_1688_suppliers(
        product_title="wireless headphones",
        target_price=15.0,
        minimum_order_qty=100,
        supplier_location="广东",
        max_suppliers=3,
        product_image_url="https://m.media-amazon.com/images/I/71GPFE2yHGL._AC_UY218_.jpg"
    )
    print(f"   Results count: {len(supplier_results)}")
    if supplier_results:
        print(f"   First supplier: {supplier_results[0]['supplier_name']}")
    else:
        print("   No results returned (expected if API fails or returns empty)")

def test_debug_export():
    """Test that debug export functionality still works"""
    print("\n" + "=" * 60)
    print("📊 Testing DEBUG EXPORT functionality")
    print("=" * 60)
    
    # Enable export
    os.environ["EXPORT_API_RESPONSES"] = "true"
    os.environ["DEBUG_EXPORT_DIR"] = "test_debug_exports"
    
    # Test in real API mode (where exports actually happen)
    os.environ["USE_MOCK_DATA"] = "false"
    
    print("\n📦 Testing Amazon search with debug export...")
    amazon_results = search_amazon_hot_products(
        keywords="test product",
        product_category="Test",
        price_range_min=10.0,
        price_range_max=50.0,
        min_rating=3.0,
        max_results=2
    )
    
    print("\n🏭 Testing 1688 search with debug export...")
    supplier_results = find_1688_suppliers(
        product_title="test product",
        target_price=10.0,
        minimum_order_qty=50,
        supplier_location="广东",
        max_suppliers=2
    )
    
    # Check if export directory was created
    export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
    if os.path.exists(export_dir):
        files = os.listdir(export_dir)
        print(f"   Export directory exists with {len(files)} files")
    else:
        print("   Export directory not created (expected if APIs not called)")

if __name__ == "__main__":
    print("🧪 Testing Mock Data Behavior Changes")
    print("This script will test both mock and real API modes")
    
    try:
        # Test mock mode
        test_mock_mode()
        
        # Test real API mode
        test_real_api_mode()
        
        # Test debug export
        test_debug_export()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
